<template>
  <a-card :bordered="false">
    <ebig-query-form ref="queryForm" :label-col="{ lg: 8, md: 8, sm: 6, xs: 6 }" @query="query" @formConfig="formConfig" is-dynamic>
      <div v-for="item in formOptions" :key="item.id" class="item">
        <component 
          :is="formConfigUtil.formItemMapper[item.inputType]" 
          :label="item.title"
          :data-source="item.iselectType==='enum'?ENUM[item.optionDataName]:DICT[item.optionDataName]"
          v-decorator="[item.fieldName]"
          allow-clear
          show-search
          :placeholder="item.oper==='between'?['开始月份','结束月份']:item.formPlaceholder"
        >
        </component>
      </div>
    </ebig-query-form>

    <ebig-operational-bar>
      <ebig-button v-action:create @click="add" />
      <ebig-button v-action:remove @click="remove" />
      <excel-import-button v-action:imports path="engineer/train-record" @uploaded="refresh" />
      <export-button v-action:exports :export="exports" />
      <ebig-button v-action:setParticipant @click="setParticipant"></ebig-button>
    </ebig-operational-bar>

    <ebig-table 
      ref="table" 
      row-key="id" 
      :columns="columns" 
      :data="loadData" 
      :row-selection="rowSelection"
      @tableConfig="handleTableConfig"
      business-type="TRAINING_RECORD"
      is-list-table
    >
      <template v-for="column in slotColumns" :slot="column.fieldName" slot-scope="text">
        <span :key="column.fieldName" v-if="column.optionType==='enum' && column.optionDataName">
          {{ ENUM[columnToEnumOrDict[column.fieldName]]?ENUM[columnToEnumOrDict[column.fieldName]][text]:text }} 
        </span>
        <span :key="column.fieldName" v-else-if="column.optionType==='dict' && column.optionDataName">
          {{ DICT[columnToEnumOrDict[column.fieldName]]?DICT[columnToEnumOrDict[column.fieldName]][text]:text }} 
        </span>
        <a-button :key="column.fieldName" v-if="text && column.picUrlFlag==1" icon="picture" size="small" @click.stop="view(text,column)">查看图片</a-button>
      </template>
      <!-- 设置参与人员 -->
      <template #engineerInfo="text,record">
        <span v-if="text">{{ text }}</span>
        <a v-else @click="setParticipant(record)">设置</a>
      </template>
      <!-- 行操作 -->
      <template #action="text,record">
        <ebig-seperated-bar>
          <a v-action:update @click.stop="edit(record)">编辑</a>
          <a @click.stop="detailed(record)">查看</a>
        </ebig-seperated-bar>
      </template>
    </ebig-table>

    <image-viewer ref="viewer" @confirm="refresh" />
    <train-record-editor ref="editor" @confirm="refresh"></train-record-editor>
    <!-- 设置参与人员 -->
    <set-participant-editor ref="setParticipant" @confirm="refresh"></set-participant-editor>
    <!-- 表单配置 -->
    <form-config-modal ref="formConfigModal" @commitFormConfig="commitFormConfig" business-type="TRAINING_RECORD"></form-config-modal>
    <!-- 表格配置 -->
    <table-config-modal ref="tableConfigModal" @commitTableConfig="commitTableConfig" business-type="TRAINING_RECORD"></table-config-modal>
  </a-card>
</template>

<script>
import { enumerationMixin } from '@/mixins/enumerationMixin'
import { tableFormConfigMixin } from '@/mixins/tableFormConfigMixin'
import { queryForm } from '@/mixins/queryForm'
import formConfigUtil from '@/utils/formConfigUtil'
import { trainRecord } from '@/api/engineer'
import TableCheckSelection from '@/mixins/TableCheckSelection'
import EbigDatePicker from '@/components/DatePicker/DatePicker.vue'
import ExcelImportButton from '@/views/.components/ExcelImportButton'
import ExportButton from '@/views/.components/ExportButton'
import trainRecordEditor from './.modal/editor.vue'
import setParticipantEditor from './.modal/setParticipant.vue'
import TableConfigModal from '@/components/Table/TableConfigModal.vue'

const componentName = 'train-manage'
export default {
  name: componentName,
  components: {
    EbigDatePicker,
    ExcelImportButton,
    ExportButton,
    trainRecordEditor,
    setParticipantEditor,
    TableConfigModal,
  },
  mixins: [
    TableCheckSelection(),
    enumerationMixin,
    tableFormConfigMixin,
    queryForm,
  ],
  data() {
    return {
      linkDictList:[],
      columns:[],
      innerColumns:[],
      formConfigUtil,
      formOptions:[],
      columnToEnumOrDict:{},
      businessType:'TRAINING_RECORD',
    }
  },
  computed: {
    form() {
      return (this.$refs.queryForm || {}).form
    },
  },
  created(){
    this.getFormItems()
    this.setTableConfig('TRAINING_RECORD','columns',['engineerInfo'],1,'right')
  },
  methods: {
    query(tag = false) {
      this.refresh({ isBackToFirstPage: true, layoutTag: tag })
    },
    refresh(option = { isBackToFirstPage: false, isResetOption: false, layoutTag: false }) {
      this.form.validateFields((err) => err || this.$refs.table.refresh(option))
    },
    loadData(pagination) {
      this.handleQuery()
      this.rowSelection.clearSelection()
      return trainRecord.getAll(Object.assign({},pagination,this.queryData)).then((res) => {
        return res
      })
    },
    add() {
      this.$refs.editor.show('add')
    },
    edit(record) {
      this.$refs.editor.show('update', record )
    },
    remove() {
      // 删除选中的行
      if (!this.rowSelection.selections.length) return this.$message.info('至少选择一条记录！')
      this.$confirm({
        title: '确认删除所选记录？删除后将无法恢复！',
        okText: '确认',
        okType: 'danger',
        cancelText: '取消',
        onOk: () =>
          trainRecord.remove(this.rowSelection.selectedRowKeys).then(() => {
            this.$message.success('删除成功')
            this.refresh()
          }),
      })
    },
    exports() {
      return trainRecord.exports({...this.queryData, ids: this.rowSelection.selectedRowKeys})
    },
    detailed(record) {
      this.$refs.editor.show('detail', record)
    },
    // 设置参与人员
    setParticipant(record) {
      if (record) return this.$refs.setParticipant.show(record)
      if (this.rowSelection.selections.length !== 1) {
        return this.$message.info('请选择一条培训记录设置参与人员')
      } else {
        this.$refs.setParticipant.show(this.rowSelection.selections[0])
      }
    },
    view(record,column) {
      this.$refs.viewer.show('view', record, column)
    },
  },
}
</script>

<style lang="scss" scoped></style>
