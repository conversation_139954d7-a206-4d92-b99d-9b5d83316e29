<template>
  <a-card :bordered="false">
    <a-row :gutter="24" style="margin-top: 35px">
      <a-col :span="14" :flex="10">
        <ebig-form ref="ladingListForm" :field-col="{ span: 16 }" :label-col="{ span: 10 }">
          <ebig-query-select
            label="供应商名称"
            v-decorator="['supplierId', { rules: [{ required: true, message: '请选择供应商名称' }] }]"
            option-title="supplierName"
            option-value="id"
            allow-clear
            :data-source="suppliers"
            :data="loadSuppliers"
            placeholder="请输入关键字查询并选择供应商"
            @select="queryParamById"
            @change="handleSupplierChange"
          />
          <ebig-input
            label="标签名称设置"
            v-decorator="['labelName', { rules: [{ required: true }, { max: 10, message: '字数最大不超过10个汉字' }] }]"
          />
          <ebig-circle-logo-upload
            label="标签左侧logo图片设置"
            v-decorator="['labelLogoPicUrl']"
            accept=".jpg,.png,.jpeg"
            :output-type="'png'"
          />
          <ebig-query-select
            ref="setFirstField"
            label="设备信息字段一设置"
            v-decorator="['fieldOne']"
            option-title="value"
            option-value="code"
            :data="getFieldList"
            @select="(index, record) => handleFormOneChange(index, record)"
            allow-clear
          />
          <ebig-query-select
            ref="setSecondField"
            label="设备信息字段二设置"
            v-decorator="['fieldTwo']"
            option-title="value"
            option-value="code"
            :data="getFieldList"
            allow-clear
            @select="(index, record) => handleFormTwoChange(index, record)"
          />
          <!--          @select="handleHospitalSelect"-->

          <ebig-query-select
            ref="setThreeField"
            label="设备信息字段三设置"
            v-decorator="['fieldThree']"
            option-title="value"
            option-value="code"
            :data="getFieldList"
            allow-clear
            @select="(index, record) => handleFormThreeChange(index, record)"
          />
          <ebig-query-select
            ref="setFourField"
            label="设备信息字段四设置"
            v-decorator="['fieldFour']"
            option-title="value"
            option-value="code"
            :data="getFieldList"
            @select="(index, record) => handleFormFourChange(index, record)"
            allow-clear
          />
        </ebig-form>
      </a-col>
      <a-col :span="10">
        <div class="println-right-box">
          <div class="println-title">{{ printTemplateTitle }}</div>
          <div class="print-right">
            <div class="print-template">
              <div class="banner">
                <!-- 利用一个标签的背景制作了大盒子的背景效果 -->
                <div class="banner-bg"></div>
                <!-- 下面是滑动焦点图部分 -->
              </div>

              <div class="print-box">
                <div class="equip-header" :class="{ 'has-logo': previewLogo, 'no-logo': !previewLogo }">
                  <!-- Logo区域 -->
                  <div v-if="previewLogo && defaultFlag == 0" class="logo-area">
                    <div class="logo-circle">
                      <img :src="previewLogo" alt="logo" />
                    </div>
                  </div>
                  <!-- 标题区域 -->
                  <div class="title-area">
                    <div class="equip-title" :style="{ textAlign: defaultFlag == 0 ? 'left' : 'center' }">
                      {{ defaultFlag == 0 ? previewLabelName : '设备管理系统标识卡' }}
                    </div>
                  </div>
                </div>
                <div class="equip-info">
                  <template v-if="defaultFlag == 0">
                    <div v-for="(field, index) in validEquipmentFields" :key="index" class="equip-field">
                      <div>{{ field.equipmentInfoFieldName }}:</div>
                      <!-- <div v-if="!defaultFlag">示例数据</div> -->
                    </div>
                  </template>

                  <!-- 如果没有配置字段，显示默认内容 -->
                  <div v-else>
                    <div class="equip-name">
                      <div>设备名称:</div>
                      <div v-if="defaultFlag == 1">全自动尿液分析系统</div>
                    </div>
                    <div class="equip-model">
                      <div>设备型号:</div>
                      <div v-if="defaultFlag == 1">US-1680</div>
                    </div>
                    <div class="equip-factory">
                      <div>生产厂家:</div>
                      <div v-if="defaultFlag == 1">桂林有特里医疗电子有限公司</div>
                    </div>
                    <div class="equip-code">
                      <div>设备编码:</div>
                      <div v-if="defaultFlag == 1">T0000000001</div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="QR-code-box">
                <img class="QR-code" src="@/assets/QRcode-template.png" alt="" />
              </div>
            </div>
          </div>

          <div class="print-btns">
            <a-button @click="handleCancel">取消</a-button>
            <a-button type="primary" @click="handleOk('preview')">预览</a-button>
            <a-button type="primary" @click="handleRestore">恢复默认</a-button>
            <a-button type="primary" key="submit" @click="handleOk('save')">保存</a-button>
          </div>
        </div>
      </a-col>
    </a-row>

    <!--弹出框-->
    <div class="print-confirm-box">
      <print-confirm ref="printConfirm" print-type="tag" :print-api="printApi" @query="query" />
    </div>
  </a-card>
</template>
<script>
import { dictionary } from '@/api/sys'
import PrintConfirm from '@/views/.modals/print'
import EbigForm from '@/components/Form/Form'
import EbigQuerySelect from '@/components/Select/QuerySelect.vue'
import EbigCircleLogoUpload from '@/components/Upload/CircleLogoUpload.vue'
import { supplier } from '@/api/org'
import equipmentLablePrintInfo from '@/api/equip/equipmentLablePrintInfo'
import { mapGetters } from 'vuex'

const componentName = 'equip-printInfo'
export default {
  name: componentName,
  data() {
    return {
      suppliers: [],
      supplierName: '',
      chooseSupplier: '',
      supplierId: '',
      isDeliver: false,
      fileOption: {
        title: '',
        name: '',
      },
      params: [],
      fieldOnes: [],
      equipmentInfo: [],
      orgEquipmentInfoFieldInfoVoList: [{}, {}, {}, {}],
      defaultOrgEquipmentInfoFieldInfoVoList: [],
      defaultFlag: '1',
      croppedLogo: '',
      form: {
        labelName: '',
        labelLogoPicUrl: '',
        fieldOne: '',
        fieldTwo: '',
        fieldThree: '',
        fieldFour: '',
      },
      // 预览数据
      previewLogo: '',
      previewLabelName: '',
    }
  },
  created() {
    dictionary.getByName('equipment_info_field').then((res) => {
      this.equipmentInfo = res.dictList
    })
    // 初始化供应商数据，默认显示当前登录用户所属供应商
    this.initSupplierData()
  },
  mounted() {
    // // 监听表单变化实时更新预览
    // this.$watch(() => {
    //   const form = this.$refs.ladingListForm?.form
    //   if (form) {
    //     return {
    //       labelName: form.getFieldValue('labelName'),
    //       labelLogoPicUrl: form.getFieldValue('labelLogoPicUrl')
    //     }
    //   }
    //   return {}
    // }, (newVal) => {
    //   this.updatePreview(newVal)
    // }, { deep: true })
  },
  components: { EbigCircleLogoUpload, EbigQuerySelect, EbigForm, PrintConfirm },
  computed: {
    ...mapGetters(['BASE_URL', 'IMAGE_BASE_URL']),
    printApi: () => (record) => {
      return equipmentLablePrintInfo.getEquipPrintPreview(record)
    },
    CurrentSuppier: (vm) => vm.$store.getters.supplierName,
    // 动态标题
    printTemplateTitle() {
      return this.defaultFlag ? '机构配置打印模板' : '默认配置打印实例'
    },
    // 有效的设备信息字段列表（过滤掉空字段）
  },
  form() {
    return this.$refs.ladingListForm.form
  },

  methods: {
    query(tag = false) {
      this.refresh({ isBackToFirstPage: true, layoutTag: tag })
    },
    //取消
    handleCancel() {
      this.getPrintInfo(this.supplierId, 'cancel')
    },
    //恢复默认
    handleRestore() {
      // const suppLier = this.$store.getters.supplierId ? this.$store.getters.supplierId : this.chooseSupplier
      // if (!this.chooseSupplier) return this.$message.warn('请先选择供应商名称！！！')
      // this.orgEquipmentInfoFieldInfoVoList = this.defaultOrgEquipmentInfoFieldInfoVoList
      // const fieldValues = {
      //   fieldOne: this.orgEquipmentInfoFieldInfoVoList[0].equipmentInfoFieldName,
      //   fieldTwo: this.orgEquipmentInfoFieldInfoVoList[1].equipmentInfoFieldName,
      //   fieldThree: this.orgEquipmentInfoFieldInfoVoList[2].equipmentInfoFieldName,
      //   fieldFour: this.orgEquipmentInfoFieldInfoVoList[3].equipmentInfoFieldName
      // }
      // if (suppLier) {
      //   this.$refs.ladingListForm.form.setFieldsValue({
      //     ...fieldValues
      //   })
      // }

      const suppLier = this.$refs.ladingListForm.form.getFieldValue('supplierId') || this.$store.getters.supplierId
      if (!suppLier) return this.$message.warn('请先选择供应商名称！！！')
      this.defaultFlag = '1'
      // this.getPrintInfo(suppLier)
    },
    //初始化供应商数据
    initSupplierData() {
      // 获取当前登录用户的供应商信息
      const currentSupplierId = this.$store.getters.supplierId
      const currentSupplierName = this.$store.getters.supplierName
      
      if (currentSupplierId && currentSupplierName) {
        // 设置默认供应商数据源
        this.suppliers = [{ id: currentSupplierId, supplierName: currentSupplierName }]
        this.supplierId = currentSupplierId
        this.supplierName = currentSupplierName
        
        // 在nextTick中设置表单默认值，确保表单已经渲染完成
        this.$nextTick(() => {
          if (this.$refs.ladingListForm && this.$refs.ladingListForm.form) {
            this.$refs.ladingListForm.form.setFieldsValue({
              supplierId: currentSupplierId,
            })
            // 自动加载该供应商的打印配置信息
            this.getPrintInfo(currentSupplierId)
          }
        })
      }
    },
    //加载供应商数据（支持关键字搜索）
    loadSuppliers(supplierName) {
      // 如果没有输入关键字，返回当前用户有权限的供应商列表
      if (!supplierName || supplierName.trim() === '') {
        return supplier.getByName('')
      }
      // 根据关键字搜索供应商
      return supplier.getByName(supplierName)
    },
    //处理供应商选择变化
    handleSupplierChange(value) {
      if (!value) {
        // 清空选择时重置相关数据
        this.supplierId = ''
        this.supplierName = ''
        this.chooseSupplier = ''
      }
    },
    //通过供应商id查询数据
    queryParamById(value, opt) {
      this.chooseSupplier = this.$refs.ladingListForm.form.getFieldValue('supplierId')
      this.supplierId = value
      this.supplierName = opt.supplierName
      this.getPrintInfo(this.supplierId)
    },
    //加载字典
    getFieldList() {
      return dictionary.getByName('equipment_info_field')
    },
    handleOk(e) {
      this.$refs.ladingListForm.form.validateFields((err, fields) => {
        if (err) return
        const { supplierId, labelName } = fields
        const orgEquipmentInfoFieldInfoVoList = JSON.parse(JSON.stringify(this.orgEquipmentInfoFieldInfoVoList))
        const params = {
          orgEquipmentInfoFieldInfoVoList: orgEquipmentInfoFieldInfoVoList,
          supplierId,
          labelName,
          supplierName: this.supplierName,
          labelLogoPicUrl: fields.labelLogoPicUrl,
        }

        if (e === 'save') {
          this.handleSave(params)
        } else {
          this.params = params
          this.$refs.printConfirm.show(params)
        }
      })
    },
    //保存标签
    handleSave(data) {
      equipmentLablePrintInfo.saveEquipPrint(data).then((res) => {
        if (res.msg === 'success') this.$message.success('保存成功')
        this.getPrintInfo(this.supplierId)
      })
    },

    //获取设备打印标签
    getPrintInfo(supplierId, status) {
      equipmentLablePrintInfo.getEquipPrint(supplierId).then((res) => {
        this.defaultOrgEquipmentInfoFieldInfoVoList =
          res.orgEquipmentLabelPrintInfo.defaultOrgEquipmentInfoFieldInfoVoList
        // 处理defaultFlag字段
        this.defaultFlag = res.orgEquipmentLabelPrintInfo.defaultFlag || false
        this.fillWithRowData(res.orgEquipmentLabelPrintInfo, status)
        this.updatePreview(res.orgEquipmentLabelPrintInfo)
        this.validEquipmentFields = res.orgEquipmentLabelPrintInfo.orgEquipmentInfoFieldInfoVoList.filter(
          (field) => field && field.equipmentInfoFieldName && field.equipmentInfoFieldName.trim() !== ''
        )
      })
    },
    //回填数据
    fillWithRowData(data, status) {
      if (data) {
        this.orgEquipmentInfoFieldInfoVoList = data?.orgEquipmentInfoFieldInfoVoList || []
        var fieldValues = {}
        if (!data.orgEquipmentInfoFieldInfoVoList) {
          fieldValues = {
            fieldOne: '',
            fieldTwo: '',
            fieldThree: '',
            fieldFour: '',
          }
        } else {
          fieldValues = {
            // fieldOne: data.orgEquipmentInfoFieldInfoVoList[0].equipmentInfoFieldName,
            // fieldTwo: data.orgEquipmentInfoFieldInfoVoList[1].equipmentInfoFieldName,
            // fieldThree: data.orgEquipmentInfoFieldInfoVoList[2].equipmentInfoFieldName,
            // fieldFour: data.orgEquipmentInfoFieldInfoVoList[3].equipmentInfoFieldName,
            ...(data.orgEquipmentInfoFieldInfoVoList.length > 0
              ? { fieldOne: data.orgEquipmentInfoFieldInfoVoList[0].equipmentInfoFieldName }
              : {}),
            ...(data.orgEquipmentInfoFieldInfoVoList.length > 1
              ? { fieldTwo: data.orgEquipmentInfoFieldInfoVoList[1].equipmentInfoFieldName }
              : {}),
            ...(data.orgEquipmentInfoFieldInfoVoList.length > 2
              ? { fieldThree: data.orgEquipmentInfoFieldInfoVoList[2].equipmentInfoFieldName }
              : {}),
            ...(data.orgEquipmentInfoFieldInfoVoList.length > 3
              ? { fieldFour: data.orgEquipmentInfoFieldInfoVoList[3].equipmentInfoFieldName }
              : {}),
          }
        }
        // 确保数组长度足够，避免越界
        if (status === 'cancel') {
          this.$refs.ladingListForm.form.setFieldsValue({
            ...this.form,
          })
        }
        this.$refs.ladingListForm.form.setFieldsValue({
          ...data, // 注意这里可能需要根据实际情况调整，避免与fieldValues中的值冲突
          ...fieldValues,
        })

        // 如果有必要初始化fieldOnes，确保逻辑正确
        this.fieldOnes = data.orgEquipmentInfoFieldInfoVoList ? [...data.orgEquipmentInfoFieldInfoVoList] : [{}]
      } else {
        this.$refs.ladingListForm.form.setFieldsValue({
          labelName: '',
          labelLogoPicUrl: '',
          fieldOne: '',
          fieldTwo: '',
          fieldThree: '',
          fieldFour: '',
        })
      }
    },
    // 定义一个通用的处理函数
    handleFormChange(index, record, i) {
      const { code, value } = record
      this.orgEquipmentInfoFieldInfoVoList[i] = {
        equipmentInfoFieldName: value,
        equipmentInfoFieldValue: code,
        orderNum: i,
      }
    },

    // 下拉列表选中项
    handleFormOneChange(index, record) {
      this.handleFormChange(index, record, 0)
    },
    handleFormTwoChange(index, record) {
      this.handleFormChange(index, record, 1)
    },
    handleFormThreeChange(index, record) {
      this.handleFormChange(index, record, 2)
    },
    handleFormFourChange(index, record) {
      this.handleFormChange(index, record, 3)
    },
    constructJSONData() {},

    // 更新预览数据
    updatePreview(formData) {
      this.previewLabelName = formData.labelName || ''
      // 处理logo图片
      if (formData.labelLogoPicUrl) {
        // 如果是相对路径，拼接图片服务器地址
        this.previewLogo = formData.labelLogoPicUrl.startsWith('http')
          ? formData.labelLogoPicUrl
          : this.$store.getters.IMAGE_BASE_URL + formData.labelLogoPicUrl
      } else {
        this.previewLogo = ''
      }
    },
  },
}
</script>

<style scoped lang="less">
::v-deep .ant-form-explain {
}

.print-left {
}

.print-template {
  width: 200px;
  height: 300px;
  overflow: hidden;
  position: relative;
  background: #3a8cfa;
}

.banner {
  margin-top: 10px;
  position: relative;
  width: 90%;
  left: 5%;
  height: 200px;
  background: #ffffff;
  //overflow: hidden;
}

.banner .banner-bg {
  position: absolute;
  //left: 5%;
  top: 85%;
  width: 100%;
  height: 30%;
  background: #ffffff;
  border-radius: 50%;
}

.print-box {
  position: absolute;
  width: 100%;
  top: 30px;
  padding-left: 20px;
  padding-right: 20px;

  // 头部区域（Logo + 标题）
  .equip-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    // 有Logo时的布局
    &.has-logo {
      justify-content: flex-start;
      gap: 12px;

      .logo-area {
        flex-shrink: 0;

        .logo-circle {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          overflow: hidden;
          border: 2px solid #3a8cfa;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

      .title-area {
        flex: 1;
        text-align: left;
      }
    }

    // 无Logo时的布局
    &.no-logo {
      justify-content: center;

      .title-area {
        text-align: center;
      }
    }

    .equip-title {
      font-size: 14px;
      font-weight: bold;
      color: #000000;
      margin: 0;
    }
  }

  // 设备信息区域
  .equip-info {
    .equip-name {
      margin-top: 0;
    }

    .equip-name,
    .equip-factory,
    .equip-code,
    .equip-model,
    .equip-field {
      display: flex;
      font-size: 12px;
      font-weight: 500;
      margin-bottom: 8px;

      :first-child {
        width: 40%;
        color: #666;
      }

      :last-child {
        width: 60%;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        color: #333;
      }
    }
  }
}

.QR-code-box {
  position: absolute;
  width: 80px;
  height: 80px;
  left: 50%;
  transform: translateX(-50%);
  bottom: 20px;
  //background: red;
  border-radius: 10px;
  //padding: 10px;
  border: 2px solid #3a8cfa;

  .QR-code {
    width: 100%;
    height: 100%;
    border-radius: 10px;
  }
}

::v-deep .ant-row-flex {
  gap: 8px;
}

.println-right-box {
  display: flex;
  width: 100%;
  align-items: center;
  flex-direction: column;
  //justify-content: center;
  .println-title {
    color: #000000;
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 20px;
  }
}

.print-btns {
  width: 60%;
  display: flex;
  flex-direction: row;
  gap: 10px;
  //justify-content: space-between;
  margin-top: 90px;
}
</style>
