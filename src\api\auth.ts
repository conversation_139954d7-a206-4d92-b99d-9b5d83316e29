import store from '@/store'
import { axios } from '@/utils/request'
import tenantUtils from '@/utils/tenant'

export function getImageCaptcha(uuid, tenantCode) {
  // 如果没有传递tenantCode，则使用智能获取方法
  const finalTenantCode = tenantCode || tenantUtils.getSmartTenantCode()
  
  if (process.env.VUE_APP_ENABLE_MOCK === 'true') {
    return Promise.resolve(require('./captcha.json'))
  } else {
    // 通过axios请求验证码，在headers中包含tenantCode
    const url = `${store.getters.CAPTCHA_URL}/captcha.jpg?uuid=${uuid}`
    const headers = finalTenantCode ? { 'tenantCode': finalTenantCode } : {}
    
    // 返回Promise，获取图片的blob数据并转换为base64
    return axios.get(url, { 
      headers,
      responseType: 'blob'
    }).then(response => {
      return new Promise((resolve) => {
        const reader = new FileReader()
        reader.onload = () => resolve(reader.result)
        reader.readAsDataURL(response.data)
      })
    }).catch(() => {
      // 如果headers方式失败，尝试URL参数方式作为fallback
      const fallbackUrl = finalTenantCode ? `${url}&tenantCode=${finalTenantCode}` : url
      return fallbackUrl
    })
  }
}
export function getCopyRight() {
  // 使用智能获取方法，只在当前路由需要时才携带tenantCode
  const tenantCode = tenantUtils.getSmartTenantCode()
  const headers = tenantCode ? { 'tenantCode': tenantCode } : {}
  return axios.post('/getWebsiteConfig', {}, { headers }).then(res=>res.mes)
}

