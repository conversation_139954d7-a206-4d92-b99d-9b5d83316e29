<template>
  <div>
    <yd-button style="margin: 0 10px;" :button="type" @click="show" />

    <!-- 弹出框 -->
    <el-dialog title="导入" :visible.sync="visible" width="520px" :close-on-click-modal="false" @close="handleCancel">
      <!-- 表单 -->
      <el-form ref="entityForm" :model="formData" :rules="rules" label-width="80px" size="small">
        <el-form-item label="上传文件" prop="file">
          <div class="file-upload">
            <el-input v-model="formData.file" readonly placeholder="请选择Excel文件" />

            <el-upload
              class="viewer"
              action="#"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleFileChange"
              accept=".xls,.xlsx"
            >
              <el-button size="small">浏览</el-button>
            </el-upload>

            <el-button
              v-if="template"
              class="template"
              size="small"
              type="text"
              @click="downloadTemplate"
            >下载模板</el-button>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="confirmLoading" @click="handleOk">上传</el-button>
      </div>
    </el-dialog>
    
    <!-- 引入导入结果对话框 -->
    <excel-download-button 
      ref="excelDownloadButton"
      @on-confirm="handleConfirm"
    ></excel-download-button>
  </div>
</template>

<script>
import dictionary, { DICT } from '@/mixins/dictionary'
import excelImportTypes from './excel-import-types'
import file from '@/api/file'
import { purchaseOrder } from '@/api/purchase-supply'
import { acceptExportData } from '@/utils/alert'
import ExcelDownloadButton from '@/views/.newComponents/ExcelDownloadButton.vue'

const componentName = 'excel-import-button'

export default {
  name: componentName,
  components: {
    ExcelDownloadButton
  },
  mixins: [...dictionary(DICT.IMPORT_TYPES)],
  props: {
    path: { type: String, required: true },
    type: { type: String, default: 'imports' },
  },
  data() {
    return {
      // 弹出框状态
      visible: false,
      confirmLoading: false,
      // 表单数据
      formData: {
        file: '',
      },
      rules: {
        file: [{ required: true, message: '请选择Excel文件', trigger: 'change' }],
      },
      // 文件对象
      file: null,
      // 错误下载文件地址
      errFileUrl: null,
    }
  },
  computed: {
    importType: (vm) => excelImportTypes[vm.path] || {},
    template: (vm) => ({ en: vm.path + '.xlsx', cn: vm.importType.template + '导入模板' }),
    importApi: (vm) =>
      vm.importType.remote
        ? (formData) => file.imports(vm.dicts.importTypes[vm.importType.remote], formData)
        : vm.importType.api,
  },
  methods: {
    // Element UI 的文件上传处理
    handleFileChange(file) {
      // file.raw 是原始文件对象
      this.file = file.raw || file
      this.formData.file = file.name || (file.raw && file.raw.name)
      return false // 阻止自动上传
    },

    // 处理下载错误数据
    handleConfirm() {
      if (!this.file || !this.errFileUrl) {
        this.$message.error('缺少文件或错误数据地址')
        return
      }
      
      const token = JSON.parse(localStorage.getItem('pro__Access-Token')).value
      const fileName = this.errFileUrl
      const aliasName = this.file.name
      
      purchaseOrder.downloadFile(token, fileName, aliasName).then((res) => {
        if (res.status === 200 && res.data) {
          acceptExportData(res)
          this.$message.success('下载成功')
        } else {
          this.$message.error('下载失败')
        }
      })
    },

    // 下载导入模板
    downloadTemplate() {
      file.getTemplate(this.template)
    },

    // 显示导入对话框
    show() {
      this.confirmLoading = false
      this.file = null
      this.formData.file = ''
      this.visible = true
      
      // 确保ExcelDownloadButton组件已经挂载
      this.$nextTick(() => {
        if (!this.$refs.excelDownloadButton) {
          console.error('ExcelDownloadButton 组件未找到!')
        }
      })
    },

    // 确保结果弹窗显示
    showResultDialog(msg, fileUrl) {
      
      // 添加延迟确保组件已更新
      this.$nextTick(() => {
        if (this.$refs.excelDownloadButton) {
          this.$refs.excelDownloadButton.showResultModal(msg || '操作完成', fileUrl)
        } else {
          // 使用Element UI自带的消息提示作为后备
          if (msg && msg.endsWith('0条记录导入失败') || msg === 'success') {
            this.$message.success('导入成功：' + msg)
          } else {
            this.$message.error('导入存在问题：' + msg)
          }
        }
      })
    },

    // 处理上传确认
    handleOk() {
      this.$refs.entityForm.validate((valid) => {
        if (!valid) return

        if (!this.file) {
          this.$message.error('请选择文件')
          return
        }

        this.confirmLoading = true
        const formData = new FormData()
        formData.append('file', this.file)

        try {
          this.importApi(formData)
            .then((res) => {
              const { msg, fileUrl } = res
              if (fileUrl) this.errFileUrl = fileUrl
              
              // 使用增强的方法显示结果弹窗
              this.showResultDialog(msg, fileUrl)
              this.$emit('uploaded')
              this.confirmLoading = false
              
              const isSuccess = msg.endsWith('0条记录导入失败') || msg === 'success'
              if (isSuccess) this.visible = false
            })
            .catch((error) => {
              this.confirmLoading = false
              this.showResultDialog('上传失败: ' + (error.message || '未知错误'))
            })
        } catch (e) {
          this.confirmLoading = false
          this.$message.error('处理上传请求时出错: ' + e.message)
        }
      })
    },

    // 取消导入
    handleCancel() {
      this.visible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.file-upload {
  display: flex;
  align-items: center;

  .el-input {
    flex: 1;
  }

  .viewer,
  .template {
    margin-left: 10px;
  }
}
</style>