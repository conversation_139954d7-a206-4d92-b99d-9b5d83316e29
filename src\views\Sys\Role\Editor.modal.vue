<template>
  <ebig-modal ref="modal" title-text="角色" :status="modalStatus" width="720px" @confirm="$emit('confirm')">
    <a-form :form="form">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="角色名称">
            <ebig-input v-decorator="['roleName', { rules: [{ required: true, message: '必填项！' }] }]" />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="所属机构">
            <!--<a-tree-select-->
            <!--  label="所属机构"-->
            <!--  searchPlaceholder="请输入要搜索的数据"-->
            <!--  v-decorator="['deptId', { rules: [{ required: true }] }]"-->
            <!--  show-search-->
            <!--  :search-value="deptSearchText"-->
            <!--  dropdown-match-select-width-->
            <!--  :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"-->
            <!--  :tree-data="treeData"-->
            <!--  tree-node-filter-prop="title"-->
            <!--  @search="(value) => (deptSearchText = value)"-->
            <!--  @blur="() => (deptSearchText = '')"-->
            <!--  @select="handleKeys"-->
            <!--  allow-clear-->
            <!--/>-->
            <a-tree-select
              v-decorator="['deptId', { rules: [{ required: true, message: '必填项！' }] }]"
              show-search
              :search-value="deptSearchText"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :tree-data="dept.treeData"
              tree-node-filter-prop="title"
              @search="(value) => (deptSearchText = value)"
              @blur="() => (deptSearchText = '')"
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="24">
          <a-form-item label="备注">
            <ebig-input v-decorator="['remark']" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="12">
          <a-form-item label="功能权限">
            <a-input-search
              placeholder="搜索功能权限"
              v-model="menu.searchValue"
              @change="handleFilterMenu"
              style="margin-bottom: 8px;"
            />
            <a-tree
              checkable
              :tree-data="menu.filteredData || menu.treeData"
              :expanded-keys="menu.expandedKeys"
              v-model="menu.checkedKeys"
              @expand="ex => handleExpand(menu, ex)"
              @check="handleMenuCheck"
            />
          </a-form-item>
        </a-col>

        <!-- <a-col :span="12">
          <a-form-item label="数据权限">
            <a-input-search placeholder="查询数据权限" v-model="dept.searchValue" @change="handleFilterDept" />
            <a-tree
              checkable
              :tree-data="dept.filteredData"
              :expanded-keys="dept.expandedKeys"
              :checked-keys="dept.checkedKeys"
              @expand="ex => handleExpand(dept, ex)"
              @check="handleDeptCheck"
            />
            <span>已选 {{ dept.checkedKeys.length }} 条记录</span>
          </a-form-item>
        </a-col> -->
      </a-row>
    </a-form>
  </ebig-modal>
</template>

<script>
import { user, menu, DATA_TYPE, department } from '@/api/sys'

import { treeSelectTypeData } from '@/utils/transformData'
import EbigModal from '@/components/Modal/Modal'
import traverseForest from '@/utils/collections/traverseForest'
import filterForestByPostorder from '@/utils/collections/filterForestByPostorder'
// import _ from '@/utils/lodash'

export default {
  name: 'role-editor',
  components: {
    EbigModal
  },
  data() {
    return {
      // 表单
      form: this.$form.createForm(this),
      isMouted:false,
      deptSearchText: '',
      middledata:[],

      // 功能权限（菜单树）
      menu: {
        searchValue: '',
        treeData: [],
        filteredData: [],
        ...emptyTreeData()
      },
      //数据权限（机构树）
      dept: {
        searchValue:'',
        treeMap: new Map(),
        treeData: [],
        filteredData: [],
        ...emptyTreeData()
      },
    }
  },
  watch:{
    deptSearchText(val){
      if (val.trim() !== '') {
        const params = {
          limit: 10,
          userType: DATA_TYPE.TREE,
          orgName: val
        }
        department.getAll(DATA_TYPE.TREE, params).then((data) => {
          this.middledata = [...this.middledata,...treeSelectTypeData(data, 'title', 'key')]

          const combinedData = [...this.dept.treeData,...this.middledata]
          const uniqueMap = new Map()
          combinedData.forEach(item => {
            uniqueMap.set(item.value, item)
          })

          // 从Map中获取去重后的数据
          const uniqueArr = Array.from(uniqueMap.values())
          console.log(
            'uniqueArr',uniqueArr
          )

          // 更新treeData为去重后的数据
          this.dept.treeData = uniqueArr
        })
      }
    },

  },
  computed: {
    modalStatus() {
      return {
        create: {
          titleTemplate: '新增${ titleText }',
          afterShow: this.reset,
          handleOk: fields => {
            const tip = this.$message.loading('正在保存...', 2)
            fields.menuIdList = this.menu.permissions
            // fields.deptIdList = this.getPermissions(this.dept)
            return user.role.add(fields).then(() => tip)
          }
        },
        update: {
          titleTemplate: '编辑${ titleText }',
          afterShow: this.fillWithRowData,
          handleOk: fields => {
            const tip = this.$message.loading('正在保存...', 2)
            fields.menuIdList = this.menu.permissions
            // fields.deptIdList = this.getPermissions(this.dept)
            fields.roleId = this.updateDataID
            return user.role.update(fields).then(() => tip)
          }
        }
      }
    }
  },
  mounted() {
    menu.getAll().then(data => {
      this.menu.treeData = treeSelectTypeData(data, 'name', 'menuId')
    })
  },
  methods: {
    show(...args) {
      this.isMouted = true
      this.$refs.modal.show(...args)
      const params = {
        limit: 5,
        userType: DATA_TYPE.TREE,
      }
      department.getAll(DATA_TYPE.TREE, params).then((data) => {
        const combinedData= [...treeSelectTypeData(data, 'title', 'key'),...this.dept.treeData]
        const uniqueMap = new Map()
        combinedData.forEach(item => {
          uniqueMap.set(item.value, item)
        })

        // 从Map中获取去重后的数据
        const uniqueArr = Array.from(uniqueMap.values())

        // 更新treeData为去重后的数据
        this.dept.treeData = uniqueArr
      })

      const [type,record]=args
      if (type==='update'){
        this.dept.treeData = [{key:record.deptId+'',title:record.deptName,value:record.deptId+''}]
      }

    },
    // 重置
    reset() {
      this.visible = true
      this.menu = {
        ...this.menu,
        ...emptyTreeData(),
        searchValue: '',
        filteredData: this.menu.treeData
      }
      // this.dept = { ...this.dept, ...emptyTreeData(), searchValue: '', filteredData: this.dept.treeData }
      this.$nextTick(() => this.form.resetFields())
    },
    fillWithRowData(record) {

      console.log(record)
      this.reset()
      this.updateDataID = record.roleId
      this.setTreeStatus(this.menu, record.menuIdList)
      // this.setTreeStatus(this.dept, record.deptIdList)
      this.$nextTick(() =>
        this.form.setFieldsValue({
          roleName: record.roleName,
          deptId: record.deptId.toString(),
          remark: record.remark
        })
      )
    },

    filterDeptByValue(input, treeNode) {
      return treeNode.data.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },

    setTreeStatus(treeStatus, keys) {
      const stringKeys = keys.map(key => key + '')
      treeStatus.permissions = stringKeys

      const { treeData, permissions } = treeStatus
      const checkedKeys = []
      traverseForest(treeData, 'children', tree => {
        if ((!tree.children || !tree.children.length) && permissions.includes(tree.key)) {
          // 勾选允许访问的叶节点
          checkedKeys.push(tree.key)
        }
      })
      treeStatus.checkedKeys = checkedKeys
    },
    /** 根据全选节点获取 permission（全选+半选 节点） */
    getPermissions(store) {
      const permissions = []
      filterForestByPostorder(store.treeData, 'children', (tree) => {
        if (!tree.children || !tree.children.length) {
          delete tree.children
        }
        if(tree.children) {
          permissions.push(tree.key)
          return true
        }
        else if (store.checkedKeys.includes(tree.key)) {
          permissions.push(tree.key)
          return true
        }
        return false
      })
      return permissions
    },
    handleExpand(store, expandedKeys = []) {
      store.expandedKeys = expandedKeys
    },
    handleMenuCheck(checkedKeys, info) {
      const store = this.menu
      store.permissions = [...info.halfCheckedKeys, ...checkedKeys]
    },
    // 搜索功能权限
    handleFilterMenu(e) {
      const value = e.target.value
      const expandedKeys = []

      this.menu.filteredData = (value === '')
        ? this.menu.treeData
        : this.filterMenu(this.menu.treeData, value, expandedKeys)

      this.menu.expandedKeys = expandedKeys
    },
    // 过滤菜单树
    filterMenu(forest, value, expandedKeys) {
      if (!forest || !forest.length) return []

      return forest.reduce((results, tree) => {
        const currentMatch = tree.title.includes(value)

        if (currentMatch) {
          results.push({ ...tree })
          traverseForest([tree], 'children', tree => {
            if ((tree.children || []).some(child => child.title.includes(value))) {
              expandedKeys.push(tree.key)
            }
          })
        } else {
          const children = this.filterMenu(tree.children, value, expandedKeys)
          if (children && children.length) {
            results.push({ ...tree, children })
            expandedKeys.push(tree.key)
          }
        }
        return results
      }, [])
    },
    // handleDeptCheck(checkedKeys, info) {
    //   // const store = this.dept
    //   const checkedKeysSet = new Set(store.checkedKeys)
    //   const checkedKey = info.node.$options.propsData.dataRef.key
    //   const node = store.treeMap.get(checkedKey)
    //   traverseForest([node], 'children', info.checked
    //     ? tree => checkedKeysSet.add(tree.key)
    //     : tree => checkedKeysSet.delete(tree.key))
    //   store.checkedKeys = [...checkedKeysSet]
    //   // NOTE: 由于处理半选节点太复杂，此处不管 permission，最后提交时才计算 permission
    // },
    // handleFilterDept: _.debounce(function handleFilterDept(e) {
    //   const value = e.target.value
    //   const expandedKeys = []

    //   this.dept.filteredData = (value === '')
    //     ? this.dept.treeData
    //     : this.filterDept(this.dept.treeData, value, expandedKeys)

    //   this.dept.expandedKeys = expandedKeys
    // }, 700),
    // filterDept(forest, value, expandedKeys) {
    //   if (!forest || !forest.length) return []

    //   return forest.reduce((results, tree) => {
    //     const currentMatch = tree.title.includes(value)

    //     if (currentMatch) {
    //       results.push({ ...tree })
    //       traverseForest([tree], 'children', tree => {
    //         if ((tree.children || []).some(child => child.title.includes(value))) {
    //           expandedKeys.push(tree.key)
    //         }
    //       })
    //     } else {
    //       const children = this.filterDept(tree.children, value, expandedKeys)
    //       if(children && children.length) {
    //         results.push({ ...tree, children })
    //         expandedKeys.push(tree.key)
    //       }
    //     }
    //     return results
    //   }, [])
    // }
  }
}

function emptyTreeData() {
  return {
    expandedKeys: [],
    checkedKeys: [],
    permissions: []
  }
}
</script>

<style lang="less" scoped>
/deep/ .ant-tree {
  max-height: 300px;
  overflow: auto;
}
</style>
