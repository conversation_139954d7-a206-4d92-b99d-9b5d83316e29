export interface FormItem {
  type: 'input' | 'select' | 'datePicker';
  label: string;
  prop: string;
  placeholder?: string;
  disabled?: boolean;
  clearable?: boolean;
  defaultValue?: any;
  rules?: Array<{
    required?: boolean;
    message?: string;
    trigger?: string | string[];
    min?: number;
    max?: number;
    pattern?: RegExp;
    validator?: (rule: any, value: any, callback: any) => void;
  }>;
  options?: Array<{
    label: string;
    value: any;
  }>;
  props?: Record<string, any>;
  optionType?: 'enum' | 'dict';
  optionDataName?: string;
}

export interface YdFormProps {
  formItems: FormItem[];
  modelValue?: Record<string, any>;
  labelWidth?: string;
  labelPosition?: 'left' | 'right' | 'top';
  inline?: boolean;
  size?: 'large' | 'default' | 'small';
  showButtons?: boolean;
  formDisabled?: boolean;
}

export interface YdFormEvents {
  'field-change': (payload: { prop: string; value: any }) => void;
  'submit': (formData: Record<string, any>) => void;
  'reset': () => void;
  'component-click': (item: FormItem, value: any) => void;
}

export interface YdFormMethods {
    validate(): Promise<boolean>;
    validateField(props: string | string[]): void;
    resetFields(): void;
    clearValidate(props?: string | string[]): void;
    submit(): Promise<Record<string, any>>;
}