<template>
  <yd-form
    ref="orderForm"
    :inline="true"
    :form-items="formOptions"
    :label-width="labelWidth"
    :show-buttons="false"
  >
  </yd-form>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'

@Component({
  name: 'FormSettingItem',
})
export default class FormSettingItem extends Vue {
  private formOptions: any[] = []
  private labelWidth = '140px'

  @Watch('formOptions', { deep: true, immediate: true })
  private onFormOptionsChange(val: any[]): void {
    if (val.length) {
      this.$nextTick(() => {
        this.setClickEvent()
      })
    }
  }

  // 表单区域点击事件
  private setClickEvent(): void {
    const orderFormEl = (this.$refs.orderForm as any)?.$el
    if (!orderFormEl) return
    const formItems = orderFormEl.querySelectorAll('.el-form-item')
    
    formItems.forEach((element) => {
      element.removeEventListener('click', this.handleClick)
    })

    formItems.forEach((element) => {
      element.addEventListener('click', this.handleClick)
    })
  }

  private handleClick(event: MouseEvent): void {
    const orderFormEl = (this.$refs.orderForm as any)?.$el
    if (!orderFormEl) return
    const formItems = orderFormEl.querySelectorAll('.el-form-item')
    formItems.forEach((el) => {
      el.style.backgroundColor = ''
    })

    const targetElement = event.currentTarget as HTMLElement
    targetElement.style.backgroundColor = 'rgb(179, 224, 252)'

    // 获取表单项的 label 文本
    const labelElement = targetElement.querySelector('.el-form-item__label')
    if (labelElement) {
      this.$emit('detail', labelElement.textContent)
    }
  }
  public clearClickBgColor(): void {
    const orderFormEl = (this.$refs.orderForm as any)?.$el
    if (!orderFormEl) return
    const formItems = orderFormEl.querySelectorAll('.el-form-item')
    formItems.forEach((el) => {
      el.style.backgroundColor = ''
    }) 
  }
}
</script>

<style lang="scss" scoped>
</style>