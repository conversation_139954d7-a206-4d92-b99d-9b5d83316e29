import { axios } from '@/utils/request'

/**
 * 菜单管理
 */
export default {

  //接口配置列表
  getList(data) {
    return axios.post('/org/orgsuppliervalidate/list', data).then(res => res.page)
  },
  //编辑
  update(data) {
    return axios.post('/org/orgsuppliervalidate/update', data)
  },
  //新增
  add(data) {
    return axios.post('/org/orgsuppliervalidate/save', data)
  },
  //删除
  del(data) {
    return axios.post('/org/orgsuppliervalidate/delete', data)
  },
  // 绑定医院权限
  bindOrg(data) {
    return axios.post('/org/orgsuppliervalidate/binding/hospitals', data)
  },
  // 获取已绑定医院机构列表
  getOrgSupplierValidate(id) {
    return axios.post(`/org/orgsuppliervalidate/hospitals?id=${id}`)
  },
  // 获取可绑定医院机构列表
  getOrgSupplierPemission() {
    return axios.post('/org/orgsuppliervalidate/hospital/pemission')
  },
  //查询绑定医院列表
  getOrgSupplierValidateList(hospitalName) {
    return axios.post('/org/orgsuppliervalidate/hospital/list', hospitalName).then(res => res.list)
  }
}