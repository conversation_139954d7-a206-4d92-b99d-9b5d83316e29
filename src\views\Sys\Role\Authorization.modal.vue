<template>
  <ebig-modal ref="modal" title-text="授权" :status="modalStatus" width="95%" @confirm="$emit('confirm')">

    <h3 class="confirmTitle">选择批量操作业务</h3>
    <div style="margin: 10px 0 10px 20px;">
      <a-radio-group v-model="batchType">
        <a-radio :value="0">批量添加所选功能授权</a-radio>
        <a-radio :value="1">批量取消所选功能授权</a-radio>
      </a-radio-group>
      <h4 style="color:red;marginTop:10px;">说明：此功能只对所选中角色进行所选功能权限的批量添加或取消操作，不影响所选角色其他的功能权限。</h4>
    </div>
    <h3 class="confirmTitle">选择功能权限</h3>
    <a-form-item label="功能权限">
      <a-tree
        checkable
        :tree-data="menu.treeData"
        :expanded-keys="menu.expandedKeys"
        v-model="menu.checkedKeys"
        @expand="ex => handleExpand(menu, ex)"
        @check="handleMenuCheck"
        style="width:50%"
      />
    </a-form-item>
    <h3 class="confirmTitle">选择角色</h3>
    <batch-role ref="batchRole"></batch-role>
  </ebig-modal>
</template>

<script>
import { menu } from '@/api/sys'
import { treeSelectTypeData } from '@/utils/transformData'
import EbigModal from '@/components/Modal/Modal'
import traverseForest from '@/utils/collections/traverseForest'
import filterForestByPostorder from '@/utils/collections/filterForestByPostorder'
import BatchRole from './BatchRole.vue'
// import _ from '@/utils/lodash'

export default {
  name: 'role-editor',
  components: {
    EbigModal,BatchRole
  },
  data() {
    return {
      batchType:0,
      // 表单
      form: this.$form.createForm(this),
      deptSearchText: '',
      // 功能权限（菜单树）
      menu: {
        treeData: [],
        ...emptyTreeData()
      },
    }
  },
  computed: {
    modalStatus() {
      return {
        batch: {
          titleTemplate: '批量${ titleText }',
          handleOk: () => {
            return this.saveBatchRole()
          },
          handleCancel:()=>{
            
          }
        }
      }
    }
  },
  mounted() {
    menu.getAll().then(data => {
      this.menu.treeData = treeSelectTypeData(data, 'name', 'menuId')
    })
  },
  methods: {
    show(...args) {
      this.$refs.modal.show(...args)
      this.$nextTick(()=>{
        this.$refs.batchRole.reset()
      })
      this.batchType = 0
      this.reset()
    },
    // 重置
    reset() {
      this.visible = true
      this.menu = { ...this.menu, ...emptyTreeData() }
      // this.dept = { ...this.dept, ...emptyTreeData(), searchValue: '', filteredData: this.dept.treeData }
      this.$nextTick(() => this.form.resetFields())
    },
    fillWithRowData(record) {
      this.reset()
      this.updateDataID = record.roleId
      this.setTreeStatus(this.menu, record.menuIdList)
      // this.setTreeStatus(this.dept, record.deptIdList)
      this.$nextTick(() =>
        this.form.setFieldsValue({
          roleName: record.roleName,
          deptId: record.deptId.toString(),
          remark: record.remark
        })
      )
    },
    
    filterDeptByValue(input, treeNode) {
      return treeNode.data.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },

    setTreeStatus(treeStatus, keys) {
      const stringKeys = keys.map(key => key + '')
      treeStatus.permissions = stringKeys
      const { treeData, permissions } = treeStatus
      const checkedKeys = []
      traverseForest(treeData, 'children', tree => {
        if ((!tree.children || !tree.children.length) && permissions.includes(tree.key)) {
          // 勾选允许访问的叶节点
          checkedKeys.push(tree.key)
        }
      })
      treeStatus.checkedKeys = checkedKeys
    },
    /** 根据全选节点获取 permission（全选+半选 节点） */
    getPermissions(store) {
      const permissions = []
      filterForestByPostorder(store.treeData, 'children', (tree) => {
        if (!tree.children || !tree.children.length) {
          delete tree.children
        }
        if(tree.children) {
          permissions.push(tree.key)
          return true
        }
        else if (store.checkedKeys.includes(tree.key)) {
          permissions.push(tree.key)
          return true
        }
        return false
      })
      return permissions
    },
    handleExpand(store, expandedKeys = []) {
      store.expandedKeys = expandedKeys
    },
    handleMenuCheck(checkedKeys, info) {
      console.log('checkedKeys', checkedKeys,info)
      const store = this.menu
      store.permissions = [...info.halfCheckedKeys, ...checkedKeys]
    },
    saveBatchRole(){
      const menuBatchAuthParam = {
        batchType:this.batchType,
        menuIdList:this.menu.checkedKeys.map((menuId)=>{return parseInt(menuId)}),
        roleIdList:this.$refs.batchRole.targets.map((row)=>row.roleId)
      }
      if(!menuBatchAuthParam.menuIdList.length)return new Promise(() => {
        this.$message.warn('请勾选功能权限')
      })
      if(!menuBatchAuthParam.roleIdList.length)return new Promise(() => {
       this.$message.warn('请选择角色')
      })
      // const tip = this.$message.loading('正在保存...', 3)
      console.log('menuBatchAuthParam', menuBatchAuthParam)
      
      // return user.role.saveBatchRole(menuBatchAuthParam).then(() => {
      //   this.$refs.batchRole.reset()
      //   tip
      // })
    }
  }
}

function emptyTreeData() {
  return {
    expandedKeys: [],
    checkedKeys: [],
    permissions: []
  }
}
</script>

<style lang="less" scoped>
/deep/ .ant-tree {
  max-height: 300px;
  overflow: auto;
  height: 200px;
}
::-webkit-scrollbar {
  width: 4px;
  height: 10px;
}
::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}
.confirmTitle {
  padding-left: 10px;
  border-left: 5px solid #29C6BD;
}
::v-deep .ant-form-item{
  margin-bottom: 10px;
}
</style>
