<template>
  <a-modal v-model="visible" :title="title" width="1400px" @cancel="handleCancel" @ok="handleOk">
    <!--合同主体信息-->
    <div>
      <div class="text-bold padding-left-df divider-top">
        <span>合同主体信息111111</span>
      </div>
      <a-divider></a-divider>
      <ebig-form ref="subjectRef" :field-col="{ span: 8 }" :label-col="{ span: 6 }">
        <ebig-query-select
          ref="organization"
          label="所属机构"
          v-decorator="['orgId', { rules: [{ required: true }] }]"
          option-title="supplierName"
          option-value="id"
          :data-source="organizations"
          :data="queryOrganization"
          allow-clear
          @select="handleOrgSelect"
          :disabled="this.type === 'view'"
        />

        <ebig-select
          label="合同类型"
          :data-source="dicts.contractType"
          placeholder="请选择"
          v-decorator="['contractType']"
          :disabled="this.type === 'view'"
        />
        <ebig-input
          :disabled="this.type === 'view'"
          v-decorator="['contractCode', { rules: [{ required: true }] }]"
          label="合同编号"
        ></ebig-input>
        <ebig-input
          v-decorator="['contractName', { rules: [{ required: true }] }]"
          :field-col="{ span: 24 }"
          :label-col="{ span: 2 }"
          :wrapper-col="{ span: 22 }"
          label="合同名称"
          :disabled="this.type === 'view'"
        ></ebig-input>
        <ebig-query-select
          v-decorator="['partyAId', { rules: [{ required: true }] }]"
          :data-source="partyInfoA"
          :data="(name) => loadQueryParty('a', name)"
          allow-clear
          label="合同甲方"
          option-title="orgName"
          option-value="orgId"
          :disabled="this.type === 'view'"
          @select="(value, record) => onSelectPartyInfo(value, record, 'A')"
          @change="(value) => handlePartyInfo(value, 'A')"
        ></ebig-query-select>
        <ebig-query-select
          v-decorator="['partyBId', { rules: [{ required: true }] }]"
          :data-source="partyInfoB"
          :data="(name) => loadQueryParty('b', name)"
          allow-clear
          option-title="orgName"
          option-value="orgId"
          label="合同乙方"
          :disabled="this.type === 'view'"
          @select="(value, record) => onSelectPartyInfo(value, record, 'B')"
          @change="(e) => handlePartyInfo(e, 'B')"
        ></ebig-query-select>
        <a-select
          v-decorator="['serviceProviderId', { rules: [{ required: true }] }]"
          :filter-option="filterOption"
          :disabled="this.type === 'view'"
          :get-popup-container="
            (triggerNode) => {
              return triggerNode.parentNode || document.body
            }
          "
          label="费用服务机构"
          mode="multiple"
          show-search
          style="width: 100%"
          @change="handleChange"
          @search="handleSearch"
        >
          <a-select-option v-for="i in originServiceProviderList" :key="`${i.orgId}${i.orgType}`">
            {{ i.orgName }}
          </a-select-option>
        </a-select>

        <!--<ebig-select label="费用服务机构" v-decorator="['serviceProviderId']" :data-source="serviceProviderList" @change="handleService"></ebig-select>-->

        <div label="合同金额:">
          <a-input-number
            :disabled="this.type === 'view'"
            v-decorator="['contractAmount', { rules: [{ required: true }] }]"
            :min="0"
            style="width: 94%"
          />
          元
        </div>
        <ebig-select
          v-decorator="['invoiceType']"
          :data-source="dicts.contractInvoiceType"
          label="发票类型"
          :disabled="this.type === 'view'"
        ></ebig-select>
        <div label="合同期限">
          <a-input-number
            :disabled="this.type === 'view'"
            v-decorator="['contractTerm', { rules: [{ required: true }] }]"
            :min="0"
            style="width: 94%"
          />
          年
        </div>
        <ebig-date-picker
          :disabled="this.type === 'view'"
          v-decorator="['beginTime', { rules: [{ required: true }] }]"
          label="合同生效时间"
        />
        <ebig-date-picker
          :disabled="this.type === 'view'"
          v-decorator="['endTime', { rules: [{ required: true }] }]"
          label="合同结束时间"
        />
        <div label="有效预警天数">
          <a-input-number
            :disabled="this.type === 'view'"
            v-decorator="['earlyDate', { rules: [{ required: true }] }]"
            :min="0"
            style="width: 94%"
          />
          天
        </div>

        <div
          :center="true"
          :field-col="{ span: 24 }"
          :label-col="{ span: 2 }"
          :wrapper-col="{ span: 22 }"
          label="合同条款"
        >
          <ebig-editor
            @ready="handleEditorReady"
            :read-only="type === 'view'"
            @editor-ready="handleEditorReady"
            ref="editor"
          ></ebig-editor>
        </div>
        <!-- <div
          class="fujian"
          :field-col="{ span: 24 }"
          :label-col="{ span: 2 }"
          :wrapper-col="{ span: 22 }"
          label="合同附件"
        >
          <ebig-drag-upload
            :readonly="type === 'view'"
            ref="uploadFill"
            v-decorator="['picUrl', { rules: [{ required: false }] }]"
            :accept="accept"
            @getImages="handleImages"
          />
        </div> -->
        <ebig-upload
        ref="fileUpload"
        :field-col="{ span: 24 }"
        :label-col="{ span: 2 }"
        :wrapper-col="{ span: 22 }"
        label="上传附件"
        :upload-url="'/charge/contractinfo/upload'"
        :init-file-list="fileList"
        accept=".pdf,.jpg,.docx"
        layout="horizontal"
      />
      </ebig-form>
    </div>

    <!--收费项目明细表-->
    <div>
      <div class="text-bold padding-left-df">
        <span>合同明细表</span>
      </div>
      <a-divider></a-divider>
      <payment-details
        ref="detailsRef"
        :readonly="type === 'view'"
        :supplier-id="currentOrgId"
        :load-data-fn="loadData"
        @handleCatalog="onHandleCatalog"
        @handleDelete="onHandleDelete"
        @handleFeeItems="onHandleFeeItems"
      ></payment-details>
    </div>

    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button v-if="type !== 'view'" type="primary" @click="handleSave(0)">保存</a-button>
      <a-button v-if="type !== 'view'" type="primary" @click="handleSave(1)">保存并发布</a-button>
    </template>
  </a-modal>
</template>

<script>
import PaymentDetails from '@/views/covenant/component/details.vue'
import ContractSubject from '@/views/payment/contract/component/subject.vue'
import contract from '@/api/payment/contract'
import EbigDatePicker from '@/components/DatePicker/DatePicker.vue'
// import EbigDragUpload from '@/components/Upload/dragUpload.vue'
import EbigUpload from '@/components/Upload/ydFileUpload.vue'

import EbigForm from '@/components/Form/Form'
import EbigEditor from '@/views/payment/contract/component/wangEditor.vue'
import dictionary, { DICT } from '@/mixins/dictionary'
import EbigQuerySelect from '@/components/Select/QuerySelect'
import { mapGetters } from 'vuex'
// import { enumeration } from '@/api/sys'

export default {
  name: 'add-contract',
  components: {
    EbigEditor,
    EbigForm,
    // EbigDragUpload,
    EbigUpload,
    EbigDatePicker,
    PaymentDetails,
    ContractSubject,
    EbigQuerySelect,
  },
  data() {
    return {
      title: '新增合同',
      accept: '.rar,.zip,.doc,.docx,.xlsx,.pdf,.jpg',
      visible: false,
      partyInfoA: [],
      type: null,
      readonly: false,
      partyInfoB: [],
      originServiceProviderList: [],
      partyA: {},
      editorIsReady: false,
      partyB: {},
      contentd: null,
      contractAttrFileInfoVos: [],
      chargeCatalogInfoVos: [],
      serviceProviderInfoVos: [],
      currentId: null,
      originPartyInfoA: [],
      originPartyInfoB: [],
      record: {},
      firstParty: {},
      SecondParty: {},
      organizations: [],
      currentOrgId: null,
      orgnizationParma: null,
      fileList:[]
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
  },
  mounted() {
    // this.fetchAndPartyInfo('a', 'partyInfoA')
    // this.fetchAndPartyInfo('b', 'partyInfoB')
    this.handleSearch('', true)
  },
  provide() {
    return {
      fillList: this.fillList,
    }
  },
  watch: {
    // type(newVal) {
    //   if (this.$refs.editor) {
    //     this.$refs.editor.editor.setReadOnly(newVal === 'view')
    //   }
    // },
  },
  mixins: [...dictionary(DICT.CONTRACT_INVOICE_TYPE, DICT.CONTRACT_TYPE, DICT.CONTRACT_CLASSIFY)],
  methods: {
    handleEditorReady() {
      console.log('编辑器已准备就绪')

      // 标记编辑器状态
      this.editorIsReady = true

      // 如果有待显示的内容，安全地填充它
      if (this.record && this.record.contentBody) {
        console.log('编辑器准备就绪，填充内容:', this.record.contentBody.length + '字符')

        // 使用 setTimeout 确保编辑器内部状态完全就绪
        setTimeout(() => {
          if (this.$refs.editor) {
            this.$refs.editor.fillHtml(this.record.contentBody)
          }
        }, 50)
      }
    },
    queryParty(tag, name = '') {
      return contract.getPartyInfoList(tag, name)
    },
    loadQueryParty(tag, name) {
      return contract.getPartyInfoList(tag, name, this.currentOrgId)
    },
    loadServiceProvidersByOrgId(orgId) {
      console.log('根据机构ID获取费用服务机构:', orgId)

      if (!orgId) {
        console.warn('机构ID为空，无法加载费用服务机构')
        return Promise.resolve([])
      }

      // 清空原有数据
      this.originServiceProviderList = []

      // 返回 Promise 以便链式调用
      return contract
        .getServiceProviderInfoList({ supplierId: orgId })
        .then((res) => {
          console.log('根据机构ID获取费用服务机构结果:', res)
          if (res && res.serviceProviderList) {
            this.originServiceProviderList = res.serviceProviderList
            this.serviceProviderList = res.serviceProviderList.reduce(
              (pre, cur) => ((pre[cur.orgId] = cur.orgName), pre),
              {}
            )
            console.log('费用服务机构列表加载完成:', this.originServiceProviderList)
            return res.serviceProviderList
          }
          return []
        })
        .catch((error) => {
          console.error('获取费用服务机构失败:', error)
          this.$message.error('获取费用服务机构信息失败')
          return []
        })
    },
    handleCancel() {
      this.visible = false
      this.initRowData()
      this.contentd = null
      this.rowSelection.clearSelection()

      this.type = true
      this.$nextTick(() => {
        this.$refs.editor.fillHtml('')
        this.$refs.editor.removeHtml()
      })
    },
    //OCR识别
    recognize() {},
    initRowData() {
      this.partyA = {}
      this.partyB = {}
      this.serviceProviderInfoVos = []
      this.chargeCatalogInfoVos = []
      this.contractAttrFileInfoVos = []
      this.currentOrgId = null // 清空当前机构ID
      this.partyInfoA = [] // 清空甲方选项
      this.partyInfoB = [] // 清空乙方选项
      this.fileList=[]
      this.$nextTick(() => {
        this.$refs.subjectRef.form.resetFields()
        this.$refs.uploadFill.fillData([])
        this.$refs.detailsRef.fillData([])
        this.$refs.editor.fillHtml('')
      })
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    handlePartyInfo(id, partyType) {
      contract.getPartyInfoList(partyType.toLowerCase()).then((res) => {
        console.log(res)
        // const upperType = partyType.toUpperCase()
        const partyList = res
        console.log(partyList)
        const selectedParty = partyList.find((party) => party.orgId == id)
        console.log(selectedParty)
        if (selectedParty) {
          this[`party${partyType}`] = {
            [`party${partyType}Id`]: selectedParty.orgId,
            [`party${partyType}Name`]: selectedParty.orgName,
            [`party${partyType}Code`]: selectedParty.orgCode,
            [`party${partyType}Type`]: selectedParty.orgType,
          }
          console.log(this[`party${partyType}`])
        }
      })
    },
    handleChange(value) {
      console.log('value', value)

      this.serviceProviderInfoVos = value
        .map((v) => {
          return this.originServiceProviderList.find((i) => `${i.orgId}${i.orgType}` === v)
        })
        .map((item) => ({
          serviceProviderId: item.orgId,
          serviceProviderName: item.orgName,
          serviceProviderCode: item.orgCode,
          serviceProviderType: item.orgType,
        }))

      console.log('this。', this.serviceProviderInfoVos)
    },
    handleSearch(val = '', type) {
      this.searchValue = val

      if (val.length !== 0 || type) {
        // 使用当前选中的机构ID或用户默认机构ID
        const supplierId = this.currentOrgId || this.userInfo.supplierId

        contract.getServiceProviderInfoList({ supplierId: supplierId }).then((res) => {
          console.log('搜索费用服务机构:', res)
          if (res && res.serviceProviderList) {
            this.originServiceProviderList = res.serviceProviderList
            this.serviceProviderList = res.serviceProviderList.reduce(
              (pre, cur) => ((pre[cur.orgId] = cur.orgName), pre),
              {}
            )
          }
        })
      } else {
        // 如果val为空，可能需要处理默认情况
        this.roleList = []
        this.roleOption = []
        this.filterDataList = []
        this.frontDataZ = []
      }
    },
    loadData(data) {
      return contract.getChargeCatalogDetailList(data)
    },

    queryOrganization(supplierName) {
      return contract.getOrgList(supplierName)
    },
    onHandleFeeItems(data) {
      console.log(data, 'onHad')
    },
    onHandleDelete(data) {
      this.chargeCatalogInfoVos = data
    },
    handleImages(uploadImages) {
      console.log(uploadImages, '=============')
      const fileAcceptMap = {
        rar: 'application/x-rar-compressed',
        zip: 'application/zip',
        doc: 'application/msword',
        docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        pdf: 'application/pdf',
      }
      const reverseFileAcceptMap = Object.entries(fileAcceptMap).reduce((acc, [key, value]) => {
        acc[value] = key
        return acc
      }, {})

      console.error(uploadImages, '允许的文件')

      this.contractAttrFileInfoVos = uploadImages.map((item) => {
        console.log('item', item)

        // 添加url存在性检查
        let fileUrl = item.url || ''

        if (fileUrl) {
          const urlParts = fileUrl.split('/')
          const startIndex = urlParts.indexOf('group1')
          if (startIndex !== -1) {
            fileUrl = urlParts.slice(startIndex).join('/')
          }
        }

        const acceptValue = reverseFileAcceptMap[item.type] || (item.name && item.name.split('.').pop()) || ''

        return {
          fileUrl: fileUrl,
          fileName: item.name || '',
          fileType: acceptValue,
          fileStatus: item.status || '',
        }
      })
    },
    show(type, record = {}) {
      this.editorIsReady = false
      this.type = type
      contract.getServiceProviderInfoList().then((res) => {
        this.originServiceProviderList = res.serviceProviderList
        this.serviceProviderList = res.serviceProviderList.reduce(
          (pre, cur) => ((pre[cur.orgId] = cur.orgName), pre),
          {}
        )
      })
      this.record = { ...record }
      if (this.$refs.editor) {
        this.$refs.editor.fillHtml('')
      }

      if (type === 'add') {
        this.visible = true

        this.title = '新增合同'
        this.initRowData()
        // 设置默认所属机构为当前用户机构
        if (this.userInfo && this.userInfo.supplierId) {
          this.organizations = [{ id: this.userInfo.supplierId, supplierName: this.userInfo.supplierName }]

          this.orgnizationParma = {
            supplierId: this.userInfo.supplierId,
            supplierName: this.userInfo.supplierName,
            supplierCode: this.userInfo.supplierCode,
          }
          this.currentOrgId = this.userInfo.supplierId

          // 根据默认机构获取费用服务机构
          this.loadServiceProvidersByOrgId(this.userInfo.supplierId)

          // this.supplierId = this.userInfo.supplierId

          this.$nextTick(() => {
            // 设置表单默认值
            this.$refs.subjectRef.form.setFieldsValue({
              orgId: this.userInfo.supplierId,
            })
          })
        }
      } else if (type === 'update') {
        this.title = '编辑合同'
        this.fillData(record)
        this.$nextTick(() => {
          // console.log('contentBody', , record)
          this.contentd = record.contentBody
          setTimeout(() => {
            this.$refs.editor.fillHtml(this.contentd || '')
          }, 500)
        })
        this.currentId = record.id
        this.visible = true
      } else {
        this.visible = true
        this.initRowData()
        this.title = '合同详情'

        this.fillData(record)
        this.$nextTick(() => {
          // console.log('contentBody', , record)
          this.contentd = record.contentBody
          setTimeout(() => {
            this.$refs.editor.fillHtml(this.contentd || '')
          }, 500)
        })
        this.currentId = record.id
        this.noedit = true
      }
    },
    onSelectPartyInfo(_, options, type) {
      const { orgId, orgName, orgCode } = options

      // 根据类型设置 partyA 或 partyB
      this[`party${type}`] = {
        [`party${type}Id`]: orgId,
        [`party${type}Name`]: orgName,
        [`party${type}Code`]: orgCode,
      }
    },

    fillData(record) {
      console.log('read',record)
      this.fileList = record.contractAttrFileInfoVos
      
      this.loadQueryParty('a')
      this.loadQueryParty('b')
      this.partyInfoA = [{ orgName: record.partyAName, orgId: record.partyAId }]
      this.partyInfoB = [{ orgName: record.partyBName, orgId: record.partyBId }]
      this.organizations = [
        {
          id: record.supplierId,
          supplierName: record.supplierName,
        },
      ]
      this.partyA = {
        partyAId: record.partyAId,
        partyAName: record.partyAName,
        partyACode: record.partyACode,
        partyAType: record.partyAType,
      }
      this.partyB = {
        partyBId: record.partyBId,
        partyBName: record.partyBName,
        partyBCode: record.partyBCode,
        partyBType: record.partyBType,
      }

      console.log('record.partyAId', record.partyAId, record.partyBId)

      this.serviceProviderInfoVos = record.serviceProviderInfoVos

      console.log('record.serviceProviderInfoVos', record.serviceProviderInfoVos)

      this.chargeCatalogInfoVos = record.chargeCatalogInfoVos
      this.contractAttrFileInfoVos = record.contractAttrFileInfoVos
      this.loadServiceProvidersByOrgId(record.supplierId)
      this.currentOrgId = record.supplierId
      // this.$refs.subjectRef.form.setFieldsValue(record)

      this.$nextTick(() => {
        this.$refs.subjectRef.form.setFieldsValue(record)

        const formattedFiles = (record.contractAttrFileInfoVos || []).map((file) => ({
          uid: `file-${Date.now()}-${Math.floor(Math.random() * 10000)}`,
          name: file.fileName,
          url: file.fileUrl,
          status: file.fileStatus || 'done',

          size: 1024, // 文件大小无法获取，设置默认值
        }))
        console.log('formattedFiles', formattedFiles, this.$refs.editor)

        this.orgnizationParma = {
          supplierId: record.supplierId,
          supplierName: record.supplierName,
          supplierCode: record.supplierCode,
        }
        this.$refs.subjectRef.form.setFieldsValue({
          orgId: record.supplierId,
          // serviceProviderId: record.serviceProviderInfoVos.map((item) => `${item.contractId}`),
        })

        console.log(
          'record.supplierId------------------------------',
          this.$refs.detailsRef,
          this.dicts.contractDDetailClassification
        )

        this.$refs.detailsRef.fillData(record.contractDetailList, this.dicts.contractDDetailClassification)
        this.$refs.uploadFill.fillData(formattedFiles)

        this.loadServiceProvidersByOrgId(record.supplierId)
          .then(() => {
            // 等待费用服务机构数据加载完成后再设置
            if (this.serviceProviderInfoVos && this.serviceProviderInfoVos.length) {
              const serviceProviderIds = this.serviceProviderInfoVos.map(
                (item) => `${item.serviceProviderId}${item.serviceProviderType}`
              )
              console.log('设置费用服务机构IDs:', serviceProviderIds)

              this.$refs.subjectRef.form.setFieldsValue({
                serviceProviderId: serviceProviderIds,
              })
            }
          })
          .catch((err) => {
            console.error('加载费用服务机构失败:', err)
          })
      })
    },
    onHandleCatalog(updatedRecord) {
      console.log(updatedRecord, 'handleCatalog')
      const index = this.chargeCatalogInfoVos.findIndex(
        (item) => item.chargeCatalogId === updatedRecord.chargeCatalogId
      )
      if (index !== -1) {
        this.chargeCatalogInfoVos[index].contractQty = updatedRecord.contractQty
      } else {
        this.chargeCatalogInfoVos.push(updatedRecord)
      }
    },
    handleOrgSelect(value, record) {
      this.orgnizationParma = {
        supplierId: record.id,
        supplierName: record.supplierName,
        supplierCode: record.supplierCode,
      }

      console.log('value', value, record)

      this.currentOrgId = value
      this.loadServiceProvidersByOrgId(value)
      // 2. 清空并重新加载甲方选项
      this.$refs.subjectRef.form.setFieldsValue({
        partyAId: undefined,
        partyBId: undefined,
        serviceProviderId: [],
      })

      // 3. 清空甲乙方信息
      this.partyA = {}
      this.partyB = {}
      this.partyInfoA = []
      this.partyInfoB = []

      // 4. 重新加载甲乙方数据
      this.loadPartyInfoByOrgId(value)
    },

    loadPartyInfoByOrgId(orgId) {
      if (!orgId) return

      // 加载甲方信息
      contract
        .getPartyInfoList('a', '', orgId)
        .then((res) => {
          this.partyInfoA = res || []
        })
        .catch((error) => {
          console.error('获取甲方信息失败:', error)
        })

      // 加载乙方信息
      contract
        .getPartyInfoList('b', '', orgId)
        .then((res) => {
          this.partyInfoB = res || []
        })
        .catch((error) => {
          console.error('获取乙方信息失败:', error)
        })
    },
    async handleSave(publishFlag) {
      const fileInfoList = this.$refs.fileUpload.getArrayFileList()
      // console.log('publishFlag', this.$refs.editor.editor.getHtml())

      // await this.$refs.subjectRef.form.validateFields()
      // await this.$refs.detailsRef.$refs.table.validate()

      console.log(this.contractAttrFileInfoVos, '========================')
      for (const item of this.contractAttrFileInfoVos) {
        if (item.fileStatus === 'error') {
          return this.$message.error(`${item.fileName}上传失败，请删除后重新保存!`)
        }
      }
      const {
        contractCode,
        contractType,
        contractName,
        contractAmount,
        invoiceType,
        contractTerm,
        beginTime,
        endTime,
        earlyDate,
      } = this.$refs.subjectRef.form.getFieldsValue()

      const params = {
        ...this.orgnizationParma,
        contractCode,
        contractType,
        contractName,
        contractAmount,
        invoiceType,
        contractTerm,
        beginTime,
        endTime,
        earlyDate,
        contentBody: this.$refs.editor.editor.getHtml(),
        publishFlag,
        ...this.partyA,
        ...this.partyB,
        serviceProviderInfoVos: this.serviceProviderInfoVos,
        contractDetailList: this.$refs.detailsRef.getData() || [],
        contractAttrFileInfoVos: fileInfoList,
      }
      console.log('params--------------------', params)

      if (this.currentId) params.id = this.currentId
      const methods = this.title === '新增合同' ? contract.addContractInfo : contract.updateContractInfo

      methods(params).then(() => {
        this.$emit('confirm')
        this.$message.success('保存成功')
        this.initRowData()
        this.visible = false
      })
    },
  },
}
</script>

<style lang="less" scoped>
.divider-top {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}

::v-deep .ant-form-inline {
  margin-right: 0 !important;
}
.fujian {
  margin-right: 0 !important;
}
::v-deep .ebig-file-upload {
  display: flex;
  gap: 10px;
  padding-bottom: 30px;
}
</style>
